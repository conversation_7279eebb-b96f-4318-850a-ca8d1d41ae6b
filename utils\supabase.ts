import AsyncStorage from '@react-native-async-storage/async-storage';
import { createClient } from '@supabase/supabase-js';
import 'react-native-url-polyfill/auto';

// Supabase configuration
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://xqyvbbspkvocjehgrfkn.supabase.co';
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhxeXZiYnNwa3ZvY2plaGdyZmtuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4MTg5NjYsImV4cCI6MjA2NjM5NDk2Nn0.l80brocsjncrXfQTn_zAr4ACfjQWZQyMnnpy1-TP7Uc';

// Create Supabase client with authentication configuration
export const supabase = createClient(supabaseUrl, supabaseKey, {
  auth: {
    storage: AsyncStorage,
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Database table names
export const TABLES = {
  USERS: 'users',
  TARGETS: 'targets', 
  TABUNGAN_DATA: 'tabungan_data',
  SETORAN_HISTORY: 'setoran_history',
  APP_SETTINGS: 'app_settings',
} as const;

// Type definitions for database tables
export interface DatabaseUser {
  id: string;
  nama: string;
  email: string;
  telepon: string;
  alamat: string;
  tanggal_lahir: string;
  created_at: string;
  updated_at: string;
}

export interface DatabaseTarget {
  id: string;
  user_id: string;
  target_biaya: number;
  tanggal_target: string;
  paket_haji: 'reguler' | 'plus' | 'khusus';
  created_at: string;
  updated_at: string;
}

export interface DatabaseTabunganData {
  id: string;
  user_id: string;
  total_tabungan: number;
  last_updated: string;
  created_at: string;
  updated_at: string;
}

export interface DatabaseSetoranHistory {
  id: string;
  user_id: string;
  tanggal: string;
  jumlah: number;
  keterangan: string;
  created_at: string;
}

export interface DatabaseAppSettings {
  id: string;
  user_id: string;
  theme: 'light' | 'dark' | 'system';
  notifications: boolean;
  language: 'id' | 'en';
  created_at: string;
  updated_at: string;
}

// Helper function to handle Supabase errors
export const handleSupabaseError = (error: any, operation: string) => {
  console.error(`Supabase ${operation} error:`, error);
  
  if (error?.message) {
    return error.message;
  }
  
  return `Gagal melakukan operasi ${operation}`;
};

// Authentication types (using Supabase's built-in types)
import type { Session, User } from '@supabase/supabase-js';

export type AuthUser = User;
export type AuthSession = Session;

// Helper function to get current authenticated user ID
export const getCurrentUserId = async (): Promise<string | null> => {
  const { data: { user } } = await supabase.auth.getUser();
  return user?.id || null;
};

// Helper function to get current session
export const getCurrentSession = async (): Promise<AuthSession | null> => {
  const { data: { session } } = await supabase.auth.getSession();
  return session;
};

// Helper function to ensure user is authenticated
export const requireAuth = async (): Promise<string> => {
  const userId = await getCurrentUserId();
  if (!userId) {
    throw new Error('User must be authenticated to perform this action');
  }
  return userId;
};

// Helper function to get current authenticated user profile
export const getCurrentUserProfile = async (): Promise<DatabaseUser | null> => {
  const userId = await getCurrentUserId();
  if (!userId) {
    return null;
  }
  return await userService.getUserById(userId);
};

// Authentication service functions
export const authService = {
  // Sign up with email and password
  async signUp(email: string, password: string, userData?: {
    nama: string;
    telepon?: string;
    alamat?: string;
    tanggal_lahir?: string;
  }) {
    console.log('Attempting sign up for email:', email);

    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: userData ? {
          nama: userData.nama,
          telepon: userData.telepon || '',
          alamat: userData.alamat || '',
          tanggal_lahir: userData.tanggal_lahir || '',
        } : undefined,
      },
    });

    if (error) {
      console.error('Supabase sign up error:', error);

      // Provide more specific error messages
      let errorMessage = 'Registration failed';

      if (error.message.includes('User already registered')) {
        errorMessage = 'Email sudah terdaftar. Silakan gunakan email lain atau login.';
      } else if (error.message.includes('Password should be at least')) {
        errorMessage = 'Password harus minimal 6 karakter.';
      } else if (error.message.includes('Invalid email')) {
        errorMessage = 'Format email tidak valid.';
      } else {
        errorMessage = error.message || 'Terjadi kesalahan saat mendaftar';
      }

      throw new Error(errorMessage);
    }

    console.log('Sign up successful:', {
      userId: data.user?.id,
      email: data.user?.email,
      emailConfirmed: !!data.user?.email_confirmed_at,
      needsVerification: !data.user?.email_confirmed_at,
      hasSession: !!data.session
    });

    return data;
  },

  // Sign in with email and password
  async signIn(email: string, password: string) {
    console.log('Attempting sign in for email:', email);

    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      console.error('Supabase sign in error:', error);

      // Provide more specific error messages
      let errorMessage = 'Login failed';

      if (error.message.includes('Invalid login credentials')) {
        errorMessage = 'Email atau password salah. Pastikan email sudah diverifikasi.';
      } else if (error.message.includes('Email not confirmed')) {
        errorMessage = 'Email belum diverifikasi. Silakan cek email Anda dan klik link verifikasi.';
      } else if (error.message.includes('Too many requests')) {
        errorMessage = 'Terlalu banyak percobaan login. Silakan tunggu beberapa menit.';
      } else {
        errorMessage = error.message || 'Terjadi kesalahan saat login';
      }

      throw new Error(errorMessage);
    }

    console.log('Sign in successful for user:', data.user?.email);
    return data;
  },

  // Sign out
  async signOut() {
    const { error } = await supabase.auth.signOut();

    if (error) {
      throw new Error(handleSupabaseError(error, 'sign out'));
    }
  },

  // Reset password
  async resetPassword(email: string) {
    const { error } = await supabase.auth.resetPasswordForEmail(email);

    if (error) {
      throw new Error(handleSupabaseError(error, 'reset password'));
    }
  },

  // Update password
  async updatePassword(newPassword: string) {
    const { error } = await supabase.auth.updateUser({
      password: newPassword,
    });

    if (error) {
      throw new Error(handleSupabaseError(error, 'update password'));
    }
  },

  // Update user metadata
  async updateUserMetadata(metadata: Record<string, any>) {
    const { error } = await supabase.auth.updateUser({
      data: metadata,
    });

    if (error) {
      throw new Error(handleSupabaseError(error, 'update user metadata'));
    }
  },

  // Get current user
  async getCurrentUser() {
    const { data: { user }, error } = await supabase.auth.getUser();

    if (error) {
      throw new Error(handleSupabaseError(error, 'get current user'));
    }

    return user;
  },

  // Listen to auth state changes
  onAuthStateChange(callback: (event: string, session: AuthSession | null) => void) {
    return supabase.auth.onAuthStateChange(callback);
  },
};

// User Service Functions
export const userService = {
  async createUser(userData: Omit<DatabaseUser, 'id' | 'created_at' | 'updated_at'>): Promise<DatabaseUser> {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .insert([{
        nama: userData.nama,
        email: userData.email,
        telepon: userData.telepon,
        alamat: userData.alamat,
        tanggal_lahir: userData.tanggal_lahir,
      }])
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'create user'));
    }

    return data;
  },

  async getUserById(userId: string): Promise<DatabaseUser | null> {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .select('*')
      .eq('id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No rows returned
      }
      throw new Error(handleSupabaseError(error, 'get user'));
    }

    return data;
  },

  async getUserByEmail(email: string): Promise<DatabaseUser | null> {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .select('*')
      .eq('email', email)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No rows returned
      }
      throw new Error(handleSupabaseError(error, 'get user by email'));
    }

    return data;
  },

  async updateUser(userId: string, updates: Partial<Omit<DatabaseUser, 'id' | 'created_at' | 'updated_at'>>): Promise<DatabaseUser> {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .update(updates)
      .eq('id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'update user'));
    }

    return data;
  },

  async deleteUser(userId: string): Promise<void> {
    const { error } = await supabase
      .from(TABLES.USERS)
      .delete()
      .eq('id', userId);

    if (error) {
      throw new Error(handleSupabaseError(error, 'delete user'));
    }
  },
};

// Target Service Functions
export const targetService = {
  async createTarget(targetData: Omit<DatabaseTarget, 'id' | 'created_at' | 'updated_at'>): Promise<DatabaseTarget> {
    const { data, error } = await supabase
      .from(TABLES.TARGETS)
      .insert([targetData])
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'create target'));
    }

    return data;
  },

  async getTargetByUserId(userId: string): Promise<DatabaseTarget | null> {
    const { data, error } = await supabase
      .from(TABLES.TARGETS)
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No rows returned
      }
      throw new Error(handleSupabaseError(error, 'get target'));
    }

    return data;
  },

  async updateTarget(userId: string, updates: Partial<Omit<DatabaseTarget, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<DatabaseTarget> {
    const { data, error } = await supabase
      .from(TABLES.TARGETS)
      .update(updates)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'update target'));
    }

    return data;
  },

  async deleteTarget(userId: string): Promise<void> {
    const { error } = await supabase
      .from(TABLES.TARGETS)
      .delete()
      .eq('user_id', userId);

    if (error) {
      throw new Error(handleSupabaseError(error, 'delete target'));
    }
  },
};

// Tabungan Data Service Functions
export const tabunganService = {
  async createTabunganData(userId: string): Promise<DatabaseTabunganData> {
    const { data, error } = await supabase
      .from(TABLES.TABUNGAN_DATA)
      .insert([{
        user_id: userId,
        total_tabungan: 0,
        last_updated: new Date().toISOString(),
      }])
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'create tabungan data'));
    }

    return data;
  },

  async getTabunganDataByUserId(userId: string): Promise<DatabaseTabunganData | null> {
    const { data, error } = await supabase
      .from(TABLES.TABUNGAN_DATA)
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No rows returned
      }
      throw new Error(handleSupabaseError(error, 'get tabungan data'));
    }

    return data;
  },

  async updateTabunganData(userId: string, updates: Partial<Omit<DatabaseTabunganData, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<DatabaseTabunganData> {
    const { data, error } = await supabase
      .from(TABLES.TABUNGAN_DATA)
      .update({
        ...updates,
        last_updated: new Date().toISOString(),
      })
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'update tabungan data'));
    }

    return data;
  },
};

// Setoran History Service Functions
export const setoranService = {
  async addSetoran(setoranData: Omit<DatabaseSetoranHistory, 'id' | 'created_at'>): Promise<DatabaseSetoranHistory> {
    const { data, error } = await supabase
      .from(TABLES.SETORAN_HISTORY)
      .insert([setoranData])
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'add setoran'));
    }

    return data;
  },

  async getSetoranHistoryByUserId(userId: string, limit?: number): Promise<DatabaseSetoranHistory[]> {
    let query = supabase
      .from(TABLES.SETORAN_HISTORY)
      .select('*')
      .eq('user_id', userId)
      .order('tanggal', { ascending: false });

    if (limit) {
      query = query.limit(limit);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(handleSupabaseError(error, 'get setoran history'));
    }

    return data || [];
  },

  async updateSetoran(setoranId: string, updates: Partial<Omit<DatabaseSetoranHistory, 'id' | 'user_id' | 'created_at'>>): Promise<DatabaseSetoranHistory> {
    const { data, error } = await supabase
      .from(TABLES.SETORAN_HISTORY)
      .update(updates)
      .eq('id', setoranId)
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'update setoran'));
    }

    return data;
  },

  async deleteSetoran(setoranId: string): Promise<void> {
    const { error } = await supabase
      .from(TABLES.SETORAN_HISTORY)
      .delete()
      .eq('id', setoranId);

    if (error) {
      throw new Error(handleSupabaseError(error, 'delete setoran'));
    }
  },

  async getSetoranStatistics(userId: string): Promise<{
    totalSetoran: number;
    averageSetoran: number;
    largestSetoran: number;
    smallestSetoran: number;
    monthlyAverage: number;
  }> {
    const { data, error } = await supabase
      .from(TABLES.SETORAN_HISTORY)
      .select('jumlah, tanggal')
      .eq('user_id', userId);

    if (error) {
      throw new Error(handleSupabaseError(error, 'get setoran statistics'));
    }

    if (!data || data.length === 0) {
      return {
        totalSetoran: 0,
        averageSetoran: 0,
        largestSetoran: 0,
        smallestSetoran: 0,
        monthlyAverage: 0,
      };
    }

    const amounts = data.map(item => item.jumlah);
    const totalSetoran = data.length;
    const totalAmount = amounts.reduce((sum, amount) => sum + amount, 0);
    const averageSetoran = totalAmount / totalSetoran;
    const largestSetoran = Math.max(...amounts);
    const smallestSetoran = Math.min(...amounts);

    // Calculate monthly average (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const recentSetoran = data.filter(
      item => new Date(item.tanggal) >= sixMonthsAgo
    );

    const monthlyAverage = recentSetoran.length > 0
      ? recentSetoran.reduce((sum, item) => sum + item.jumlah, 0) / 6
      : 0;

    return {
      totalSetoran,
      averageSetoran,
      largestSetoran,
      smallestSetoran,
      monthlyAverage,
    };
  },
};

// App Settings Service Functions
export const settingsService = {
  async createSettings(userId: string, settings?: Partial<Omit<DatabaseAppSettings, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<DatabaseAppSettings> {
    const { data, error } = await supabase
      .from(TABLES.APP_SETTINGS)
      .insert([{
        user_id: userId,
        theme: settings?.theme || 'system',
        notifications: settings?.notifications ?? true,
        language: settings?.language || 'id',
      }])
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'create settings'));
    }

    return data;
  },

  async getSettingsByUserId(userId: string): Promise<DatabaseAppSettings | null> {
    const { data, error } = await supabase
      .from(TABLES.APP_SETTINGS)
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return null; // No rows returned
      }
      throw new Error(handleSupabaseError(error, 'get settings'));
    }

    return data;
  },

  async updateSettings(userId: string, updates: Partial<Omit<DatabaseAppSettings, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<DatabaseAppSettings> {
    const { data, error } = await supabase
      .from(TABLES.APP_SETTINGS)
      .update(updates)
      .eq('user_id', userId)
      .select()
      .single();

    if (error) {
      throw new Error(handleSupabaseError(error, 'update settings'));
    }

    return data;
  },

  async deleteSettings(userId: string): Promise<void> {
    const { error } = await supabase
      .from(TABLES.APP_SETTINGS)
      .delete()
      .eq('user_id', userId);

    if (error) {
      throw new Error(handleSupabaseError(error, 'delete settings'));
    }
  },
};

// Combined service functions for easier migration from storage.ts
export const combinedService = {
  // Initialize user with all related data
  async initializeUser(userData: {
    nama: string;
    email: string;
    telepon: string;
    alamat: string;
    tanggal_lahir: string;
  }, targetData?: {
    target_biaya: number;
    tanggal_target: string;
    paket_haji: 'reguler' | 'plus' | 'khusus';
  }): Promise<{
    user: DatabaseUser;
    target?: DatabaseTarget;
    tabunganData: DatabaseTabunganData;
    settings: DatabaseAppSettings;
  }> {
    // Create user
    const user = await userService.createUser({
      nama: userData.nama,
      email: userData.email,
      telepon: userData.telepon,
      alamat: userData.alamat,
      tanggal_lahir: userData.tanggal_lahir,
    });

    // Create tabungan data
    const tabunganData = await tabunganService.createTabunganData(user.id);

    // Create settings
    const settings = await settingsService.createSettings(user.id);

    // Create target if provided
    let target: DatabaseTarget | undefined;
    if (targetData) {
      target = await targetService.createTarget({
        user_id: user.id,
        target_biaya: targetData.target_biaya,
        tanggal_target: targetData.tanggal_target,
        paket_haji: targetData.paket_haji,
      });
    }

    return {
      user,
      target,
      tabunganData,
      settings,
    };
  },

  // Get all user data
  async getAllUserData(userId: string): Promise<{
    user: DatabaseUser | null;
    target: DatabaseTarget | null;
    tabunganData: DatabaseTabunganData | null;
    setoranHistory: DatabaseSetoranHistory[];
    settings: DatabaseAppSettings | null;
  }> {
    const [user, target, tabunganData, setoranHistory, settings] = await Promise.all([
      userService.getUserById(userId),
      targetService.getTargetByUserId(userId),
      tabunganService.getTabunganDataByUserId(userId),
      setoranService.getSetoranHistoryByUserId(userId),
      settingsService.getSettingsByUserId(userId),
    ]);

    return {
      user,
      target,
      tabunganData,
      setoranHistory,
      settings,
    };
  },
};

// Authenticated service functions (use current authenticated user)
export const authUserService = {
  // Get current user's profile
  async getCurrentUserProfile(): Promise<DatabaseUser | null> {
    const userId = await getCurrentUserId();
    if (!userId) return null;
    return await userService.getUserById(userId);
  },

  // Create or update current user's profile
  async upsertCurrentUserProfile(userData: Omit<DatabaseUser, 'id' | 'created_at' | 'updated_at'>): Promise<DatabaseUser> {
    const userId = await requireAuth();

    // Try to get existing user first
    const existingUser = await userService.getUserById(userId);

    if (existingUser) {
      // Update existing user
      return await userService.updateUser(userId, userData);
    } else {
      // Create new user with auth user ID
      const { data, error } = await supabase
        .from(TABLES.USERS)
        .insert([{
          id: userId, // Use auth user ID
          nama: userData.nama,
          email: userData.email,
          telepon: userData.telepon,
          alamat: userData.alamat,
          tanggal_lahir: userData.tanggal_lahir,
        }])
        .select()
        .single();

      if (error) {
        throw new Error(handleSupabaseError(error, 'create user profile'));
      }

      return data;
    }
  },

  // Get current user's target
  async getCurrentUserTarget(): Promise<DatabaseTarget | null> {
    const userId = await getCurrentUserId();
    if (!userId) return null;
    return await targetService.getTargetByUserId(userId);
  },

  // Create or update current user's target
  async upsertCurrentUserTarget(targetData: Omit<DatabaseTarget, 'id' | 'user_id' | 'created_at' | 'updated_at'>): Promise<DatabaseTarget> {
    const userId = await requireAuth();

    const existingTarget = await targetService.getTargetByUserId(userId);

    if (existingTarget) {
      return await targetService.updateTarget(userId, targetData);
    } else {
      return await targetService.createTarget({
        user_id: userId,
        ...targetData,
      });
    }
  },

  // Get current user's tabungan data
  async getCurrentUserTabunganData(): Promise<DatabaseTabunganData | null> {
    const userId = await getCurrentUserId();
    if (!userId) return null;
    return await tabunganService.getTabunganDataByUserId(userId);
  },

  // Initialize or get current user's tabungan data
  async initCurrentUserTabunganData(): Promise<DatabaseTabunganData> {
    const userId = await requireAuth();

    const existingData = await tabunganService.getTabunganDataByUserId(userId);

    if (existingData) {
      return existingData;
    } else {
      return await tabunganService.createTabunganData(userId);
    }
  },

  // Update current user's tabungan data
  async updateCurrentUserTabunganData(updates: Partial<Omit<DatabaseTabunganData, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<DatabaseTabunganData> {
    const userId = await requireAuth();
    return await tabunganService.updateTabunganData(userId, updates);
  },

  // Get current user's setoran history
  async getCurrentUserSetoranHistory(limit?: number): Promise<DatabaseSetoranHistory[]> {
    const userId = await getCurrentUserId();
    if (!userId) return [];
    return await setoranService.getSetoranHistoryByUserId(userId, limit);
  },

  // Add setoran for current user
  async addCurrentUserSetoran(setoranData: Omit<DatabaseSetoranHistory, 'id' | 'user_id' | 'created_at'>): Promise<DatabaseSetoranHistory> {
    const userId = await requireAuth();
    return await setoranService.addSetoran({
      user_id: userId,
      ...setoranData,
    });
  },

  // Get current user's setoran statistics
  async getCurrentUserSetoranStatistics() {
    const userId = await getCurrentUserId();
    if (!userId) {
      return {
        totalSetoran: 0,
        averageSetoran: 0,
        largestSetoran: 0,
        smallestSetoran: 0,
        monthlyAverage: 0,
      };
    }
    return await setoranService.getSetoranStatistics(userId);
  },

  // Get current user's settings
  async getCurrentUserSettings(): Promise<DatabaseAppSettings | null> {
    const userId = await getCurrentUserId();
    if (!userId) return null;
    return await settingsService.getSettingsByUserId(userId);
  },

  // Initialize or get current user's settings
  async initCurrentUserSettings(settings?: Partial<Omit<DatabaseAppSettings, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<DatabaseAppSettings> {
    const userId = await requireAuth();

    const existingSettings = await settingsService.getSettingsByUserId(userId);

    if (existingSettings) {
      return existingSettings;
    } else {
      return await settingsService.createSettings(userId, settings);
    }
  },

  // Update current user's settings
  async updateCurrentUserSettings(updates: Partial<Omit<DatabaseAppSettings, 'id' | 'user_id' | 'created_at' | 'updated_at'>>): Promise<DatabaseAppSettings> {
    const userId = await requireAuth();
    return await settingsService.updateSettings(userId, updates);
  },

  // Get all current user's data
  async getAllCurrentUserData() {
    const userId = await getCurrentUserId();
    if (!userId) {
      return {
        user: null,
        target: null,
        tabunganData: null,
        setoranHistory: [],
        settings: null,
      };
    }
    return await combinedService.getAllUserData(userId);
  },
};
