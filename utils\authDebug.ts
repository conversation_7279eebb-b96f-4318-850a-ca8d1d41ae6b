import { supabase } from './supabase';

// Debug utility for authentication issues
export const authDebug = {
  // Test Supabase connection
  async testConnection() {
    try {
      const { data, error } = await supabase.auth.getSession();
      console.log('Supabase connection test:', { 
        success: !error, 
        hasSession: !!data.session,
        error: error?.message 
      });
      return !error;
    } catch (err) {
      console.error('Connection test failed:', err);
      return false;
    }
  },

  // Check if user exists (for debugging purposes)
  async checkUserExists(email: string) {
    try {
      // Try to trigger password reset to see if user exists
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: 'https://example.com', // dummy redirect
      });
      
      if (error) {
        console.log('User check result:', error.message);
        return { exists: false, message: error.message };
      }
      
      return { exists: true, message: 'User exists' };
    } catch (err) {
      console.error('Error checking user:', err);
      return { exists: false, message: 'Error checking user' };
    }
  },

  // Get current auth state
  async getCurrentAuthState() {
    try {
      const { data: { session } } = await supabase.auth.getSession();
      const { data: { user } } = await supabase.auth.getUser();
      
      console.log('Current auth state:', {
        hasSession: !!session,
        hasUser: !!user,
        userEmail: user?.email,
        emailConfirmed: user?.email_confirmed_at,
        lastSignIn: user?.last_sign_in_at,
      });
      
      return { session, user };
    } catch (err) {
      console.error('Error getting auth state:', err);
      return { session: null, user: null };
    }
  },

  // Test registration with a dummy email
  async testRegistration(testEmail: string = '<EMAIL>', testPassword: string = 'test123456') {
    try {
      console.log('Testing registration with:', testEmail);
      
      const { data, error } = await supabase.auth.signUp({
        email: testEmail,
        password: testPassword,
      });

      if (error) {
        console.log('Registration test failed:', error.message);
        return { success: false, error: error.message };
      }

      console.log('Registration test result:', {
        success: true,
        needsVerification: !data.user?.email_confirmed_at,
        userId: data.user?.id,
      });

      return { success: true, data };
    } catch (err) {
      console.error('Registration test error:', err);
      return { success: false, error: 'Unexpected error' };
    }
  },

  // Test login with credentials
  async testLogin(email: string, password: string) {
    try {
      console.log('Testing login with:', email);
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        console.log('Login test failed:', error.message);
        return { success: false, error: error.message };
      }

      console.log('Login test successful:', {
        userId: data.user?.id,
        email: data.user?.email,
        emailConfirmed: data.user?.email_confirmed_at,
      });

      return { success: true, data };
    } catch (err) {
      console.error('Login test error:', err);
      return { success: false, error: 'Unexpected error' };
    }
  },

  // Run comprehensive auth diagnostics
  async runDiagnostics() {
    console.log('=== AUTH DIAGNOSTICS ===');
    
    // Test connection
    const connectionOk = await this.testConnection();
    console.log('1. Connection:', connectionOk ? '✅' : '❌');
    
    // Get current state
    await this.getCurrentAuthState();
    
    // Test with a known email if provided
    console.log('=== END DIAGNOSTICS ===');
    
    return {
      connection: connectionOk,
      timestamp: new Date().toISOString(),
    };
  }
};

// Export for easy access in development
if (__DEV__) {
  // Make it available globally for debugging
  (global as any).authDebug = authDebug;
}
